from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from app.routers import (
    auth,
    users,
    courses,
    modules,
    chatbots,
    chat_sessions,
    messages,
    notifications,
    internal_messages,
    groups,
    enrollments,
    video,
    avatars,
    database,
)
from app.database import (
    get_engine, 
    Base, 
    check_database_connection,
    validate_database_setup,
    print_database_setup_guide
)
import logging
import sys

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Validate database setup at startup
logger.info("🔍 Validating database setup...")
validation_results = validate_database_setup()

# Check if engine creation succeeded (most important check)
engine_created = validation_results["checks"].get("engine_creation", {}).get("status") == "passed"

if validation_results["overall_status"] == "failed" and not engine_created:
    logger.error("❌ Database setup validation failed!")
    for check_name, check_result in validation_results["checks"].items():
        if check_result["status"] == "failed":
            logger.error(f"   • {check_name}: {check_result['message']}")
    
    if validation_results["recommendations"]:
        logger.error("💡 Recommendations:")
        for rec in validation_results["recommendations"]:
            logger.error(f"   • {rec}")
    
    logger.error("📚 Check the detailed configuration guide...")
    print_database_setup_guide()
    sys.exit(1)

elif validation_results["overall_status"] == "failed" and engine_created:
    logger.warning("⚠️ Database has some issues but engine creation succeeded - continuing...")
    for check_name, check_result in validation_results["checks"].items():
        if check_result["status"] == "failed":
            logger.warning(f"   • {check_name}: {check_result['message']}")
        elif check_result["status"] == "warning":
            logger.info(f"   • {check_name}: {check_result['message']}")
    
    if validation_results["recommendations"]:
        logger.info("💡 Note:")
        for rec in validation_results["recommendations"]:
            logger.info(f"   • {rec}")
    print()

elif validation_results["overall_status"] == "warning":
    logger.warning("⚠️ Database setup has warnings:")
    for check_name, check_result in validation_results["checks"].items():
        if check_result["status"] == "warning":
            logger.warning(f"   • {check_name}: {check_result['message']}")
    
    if validation_results["recommendations"]:
        logger.info("💡 Recommendations:")
        for rec in validation_results["recommendations"]:
            logger.info(f"   • {rec}")
    print()

else:
    logger.info("✅ Database setup validation passed!")

# Skip automatic table creation for existing databases
# Note: For new databases, uncomment the following lines:
# try:
#     engine = get_engine()
#     Base.metadata.create_all(bind=engine)
#     logger.info("✅ Database tables created/updated successfully")
# except Exception as e:
#     logger.warning(f"⚠️ Failed to create database tables during startup: {e}")

logger.info("🔄 Skipping automatic table creation (existing database detected)")
logger.info("💡 If you need to add new tables/fields, use database migration tools")
logger.info("📚 For manual schema changes, check the updated models.py for new field definitions")

# Lifespan event handler (modern replacement for startup/shutdown events)
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("🚀 Bytewise Backend API Started")
    logger.info("📊 Database connection pool initialized")
    logger.info("🌐 Main monitoring endpoints:")
    logger.info("   • Health check: GET /health")
    logger.info("   • Database status: GET /database/status")
    logger.info("   • Configuration validation: GET /database/validate")
    logger.info("📚 Full API documentation: http://localhost:8000/docs")
    logger.info("✅ Service is ready to receive requests!")
    
    yield
    
    # Shutdown
    logger.info("👋 Bytewise Backend API shutting down...")

app = FastAPI(
    title="Bytewise Backend API",
    description="Backend API for Bytewise Learning Platform with Supabase integration",
    version="3.0.0",
    lifespan=lifespan
)

# Permitted CORS Sources
origins = [
    "http://localhost:9000",  # Add your front-end development address
    "http://127.0.0.1:9000",  # If you have another front-end address, you can also add it here
    "http://127.0.0.1:4000",
    "https://bytewisehk.netlify.app",
    "https://bytewise-v3.netlify.app",
    "https://bytewise-v3-test.netlify.app",
    "https://bytewise-v3-release.netlify.app",
    "https://new.bytewise.hk",
    "https://chat.hkbu.life",
    "http://*************",
]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # Permitted sources
    allow_credentials=True,  # Permitted credentials
    allow_methods=["*"],  # Permitted HTTP methods
    allow_headers=["*"],  # Permitted HTTP headers
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Simple health check endpoint to verify basic database connectivity"""
    db_status = check_database_connection()
    return {
        "status": "healthy" if db_status else "unhealthy",
        "database": "connected" if db_status else "disconnected",
        "service": "Bytewise Backend API",
        "version": "3.0.0"
    }



# Include routers
app.include_router(auth.router)
app.include_router(users.router)
app.include_router(courses.router)
app.include_router(modules.router)
app.include_router(chatbots.router)
app.include_router(chat_sessions.router)
app.include_router(messages.router)
app.include_router(notifications.router)
app.include_router(internal_messages.router)
app.include_router(groups.router)
app.include_router(enrollments.router)
app.include_router(video.router)
app.include_router(avatars.router)
app.include_router(database.router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
