from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload, contains_eager
from pydantic import BaseModel
import datetime
from typing import List
import uuid

from .. import models
from ..database import get_db
from ..dependencies import authenticate_user

# Add relationships to models for easier querying
models.Module.avatars = models.relationship("ModuleAvatar", back_populates="module")
models.ModuleAvatar.module = models.relationship("Module", back_populates="avatars")
models.ModuleAvatar.avatar = models.relationship("Avatar", back_populates="module_associations")
models.Avatar.module_associations = models.relationship("ModuleAvatar", back_populates="avatar")
models.Avatar.creator = models.relationship("User", foreign_keys=[models.Avatar.creator_user_id])


router = APIRouter(
    prefix="/api",
    tags=["avatars"],
)


@router.delete("/delete_avatar")
async def delete_avatar(avatar_id: str, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="You are not authorized to delete a chatbot")

    avatar = db.query(models.Avatar).filter(models.Avatar.id == avatar_id).first()
    if not avatar:
        raise HTTPException(status_code=404, detail="Avatar not found")

    avatar.deleted_at = datetime.datetime.now(datetime.timezone.utc)
    db.commit()
    db.refresh(avatar)
    return avatar


@router.get("/get_avatar_knowledge/{avatar_id}")
async def get_avatar_knowledge(avatar_id: str, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    avatar = db.query(models.Avatar).filter(
        models.Avatar.id == avatar_id,
        models.Avatar.creator_user_id == user_id
    ).first()

    if not avatar:
        raise HTTPException(status_code=404, detail="Avatar not found or access denied")

    return {"avatar": {"knowledge_base": avatar.knowledge_base}}


@router.delete("/delete-module-avatar")
async def delete_module_avatar(module_id: str, avatar_id: str, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Student cannot delete module chatbot")

    module_avatar = db.query(models.ModuleAvatar).filter(
        models.ModuleAvatar.module_id == module_id,
        models.ModuleAvatar.avatar_id == avatar_id
    ).first()

    if not module_avatar:
        raise HTTPException(status_code=404, detail="Module avatar association not found")

    module_avatar.deleted_at = datetime.datetime.now(datetime.timezone.utc)
    db.commit()
    db.refresh(module_avatar)
    return module_avatar


@router.get("/module-avatar-list")
async def get_module_avatar_list(course_id: str = Query(...), db: Session = Depends(get_db)):
    # Query module-avatar information from the view
    module_avatar_items = db.query(models.ModuleAvatarItemView).filter(
        models.ModuleAvatarItemView.course_id == course_id
    ).all()

    # Group by module_id (similar to original Supabase implementation)
    modules_dict = {}
    for item in module_avatar_items:
        module_id = item.module_id
        # Remember to handle with module_deleted_at field
        if module_id not in modules_dict and not item.module_deleted_at:
            modules_dict[module_id] = {
                "module_id": module_id,
                "course_id": item.course_id,
                "avatars": [],
            }
        # Remember to handle with all deleted_ats field
        if (not item.module_deleted_at and 
            not item.avatar_deleted_at and 
            not item.module_avatar_deleted_at):
            modules_dict[module_id]["avatars"].append({
                "avatar_id": item.avatar_id,
                "avatar_name": item.avatar_name,
                "created_at": item.avatar_created_at,
                "creator_user_id": item.creator_user_id,
                "creator_user_full_name": item.creator_user_full_name,
            })

    # Convert dictionary to list
    modules_with_avatars = list(modules_dict.values())

    return modules_with_avatars


class ModuleAvatarData(BaseModel):
    module_id: str
    avatar_ids: List[str]


@router.post("/module-avatars")
async def add_module_avatars(data: ModuleAvatarData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    new_associations = []
    for avatar_id in data.avatar_ids:
        new_association = models.ModuleAvatar(
            id=str(uuid.uuid4()),
            module_id=data.module_id,
            avatar_id=avatar_id
        )
        new_associations.append(new_association)
    
    db.add_all(new_associations)
    db.commit()
    return new_associations


@router.get("/avatar-list-by-module")
async def get_avatar_list_by_module(module_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    try:
        # 1. Get user role
        user = db.query(models.User).filter(models.User.user_id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail=f"User not found (user_id: {user_id})")

        user_role = user.role_name
        if user_role != "Teacher":
            raise HTTPException(
                status_code=403,
                detail=f"Permission denied. Required role: Teacher, your role: {user_role}",
            )

        # 2. Get IDs of avatars already associated with the module
        associated_avatar_ids_result = db.query(models.ModuleAvatar.avatar_id).filter(
            models.ModuleAvatar.module_id == module_id,
            models.ModuleAvatar.deleted_at.is_(None)
        ).all()
        
        # 3. Extract avatar_id list
        avatar_ids = [a_id for (a_id,) in associated_avatar_ids_result if a_id is not None]

        # Query avatars that are not associated with the current module
        # Use correct field name creator_user_id
        avatars = db.query(models.Avatar).filter(
            models.Avatar.creator_user_id == user_id,
            models.Avatar.deleted_at.is_(None)
        ).all()
        
        if not avatars:
            return []

        # Filter out avatars that are already associated with the module
        filtered_avatars = [
            avatar for avatar in avatars 
            if avatar.id not in avatar_ids
        ]

        if not filtered_avatars:
            return []

        # Convert to dict format to avoid SQLAlchemy serialization issues
        result = []
        for avatar in filtered_avatars:
            avatar_dict = {
                "id": avatar.id,
                "creator_user_id": avatar.creator_user_id,
                "avatar_name": avatar.avatar_name,
                "knowledge_base": avatar.knowledge_base,
                "created_at": avatar.created_at,
                "updated_at": avatar.updated_at,
                "deleted_at": avatar.deleted_at
            }
            result.append(avatar_dict)

        return result

    except HTTPException:
        raise
    except Exception as e:
        # Return detailed error information
        raise HTTPException(
            status_code=500, detail=f"Internal error occurred: {str(e)}"
        )


class AvatarCreateData(BaseModel):
    name: str
    knowledge: dict

@router.post("/create_new_avatar")
async def create_new_avatar(data: AvatarCreateData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Only Teachers are authorized to create avatars")
    
    new_avatar = models.Avatar(
        id=str(uuid.uuid4()),
        creator_user_id=user_id,
        avatar_name=data.name,
        knowledge_base=data.knowledge
    )
    db.add(new_avatar)
    db.commit()
    db.refresh(new_avatar)
    return new_avatar


@router.get("/get_avatar_list")
async def get_avatar_list(user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    avatars = db.query(models.Avatar).filter(
        models.Avatar.creator_user_id == user_id,
        models.Avatar.deleted_at.is_(None)
    ).all()
    return avatars


class AvatarUpdateData(BaseModel):
    name: str
    knowledge: dict

@router.put("/update_avatar/{avatar_id}")
async def update_avatar(avatar_id: str, data: AvatarUpdateData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Only Teachers are authorized to update avatars")

    avatar = db.query(models.Avatar).filter(
        models.Avatar.id == avatar_id,
        models.Avatar.creator_user_id == user_id
    ).first()

    if not avatar:
        raise HTTPException(status_code=404, detail="Avatar not found or you don't have permission to edit it")

    avatar.avatar_name = data.name
    avatar.knowledge_base = data.knowledge
    avatar.updated_at = datetime.datetime.now(datetime.timezone.utc)
    
    db.commit()
    db.refresh(avatar)
    return avatar
